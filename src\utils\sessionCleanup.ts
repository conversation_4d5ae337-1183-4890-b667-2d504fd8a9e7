import { cleanupExpiredSessions } from './sessionManager';

// Session cleanup interval in milliseconds
let cleanupInterval: NodeJS.Timeout | null = null;

// Parse time string to milliseconds
const parseTimeToMs = (timeString: string): number => {
  const timeMatch = timeString.match(/^(\d+)([dhm])$/);
  if (!timeMatch) {
    // Default to 1 hour if invalid format
    return 60 * 60 * 1000;
  }

  const [, amount, unit] = timeMatch;
  const amountNum = parseInt(amount, 10);

  switch (unit) {
    case 'd': // days
      return amountNum * 24 * 60 * 60 * 1000;
    case 'h': // hours
      return amountNum * 60 * 60 * 1000;
    case 'm': // minutes
      return amountNum * 60 * 1000;
    default:
      return 60 * 60 * 1000; // Default to 1 hour
  }
};

// Start automatic session cleanup
export const startSessionCleanup = (): void => {
  const cleanupIntervalTime = process.env.SESSION_CLEANUP_INTERVAL || '1h';
  const intervalMs = parseTimeToMs(cleanupIntervalTime);

  console.log(`[SESSION CLEANUP] Starting automatic cleanup every ${cleanupIntervalTime}`);

  // Run cleanup immediately
  performCleanup();

  // Set up recurring cleanup
  cleanupInterval = setInterval(performCleanup, intervalMs);
};

// Stop automatic session cleanup
export const stopSessionCleanup = (): void => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
    console.log('[SESSION CLEANUP] Automatic cleanup stopped');
  }
};

// Perform cleanup operation
const performCleanup = async (): Promise<void> => {
  try {
    console.log('[SESSION CLEANUP] Starting cleanup operation');
    const deletedCount = await cleanupExpiredSessions();
    console.log(`[SESSION CLEANUP] Cleaned up ${deletedCount} expired sessions`);
  } catch (error: any) {
    console.error('[SESSION CLEANUP] Error during cleanup:', error.message);
  }
};

// Manual cleanup function
export const manualCleanup = async (): Promise<number> => {
  try {
    console.log('[SESSION CLEANUP] Manual cleanup requested');
    const deletedCount = await cleanupExpiredSessions();
    console.log(`[SESSION CLEANUP] Manual cleanup completed: ${deletedCount} sessions removed`);
    return deletedCount;
  } catch (error: any) {
    console.error('[SESSION CLEANUP] Error during manual cleanup:', error.message);
    throw error;
  }
};

// Get cleanup status
export const getCleanupStatus = (): { isRunning: boolean; intervalMs: number } => {
  const cleanupIntervalTime = process.env.SESSION_CLEANUP_INTERVAL || '1h';
  const intervalMs = parseTimeToMs(cleanupIntervalTime);
  
  return {
    isRunning: cleanupInterval !== null,
    intervalMs
  };
};

// Graceful shutdown handler
export const gracefulShutdown = (): void => {
  console.log('[SESSION CLEANUP] Graceful shutdown initiated');
  stopSessionCleanup();
  
  // Perform final cleanup
  performCleanup().then(() => {
    console.log('[SESSION CLEANUP] Final cleanup completed');
  }).catch((error) => {
    console.error('[SESSION CLEANUP] Error during final cleanup:', error.message);
  });
};
