
import mongoose, { Schema } from "mongoose";
import { Role, Status, User } from "../interfaces/user";




const userSchema: Schema<User> = new Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true, index: true },
  password: { type: String, required: true },
  profileImage: { type: String, default: "" },
  role: { type: String, enum: Object.values(Role), default: "user" },
  status: { type: String, enum: Object.values(Status), default: "inactivated" },
},
  { versionKey: false,
    timestamps: true }
);

userSchema.index({ email: 1, status: 1 });

const userModel = mongoose.model<User>("User", userSchema);

export default userModel;
