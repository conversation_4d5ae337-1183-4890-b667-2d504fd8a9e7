import express, { Request<PERSON>and<PERSON> } from "express";
import {
  getUserSessions,
  revokeUserSession,
  revokeMultipleSessions,
  revokeAllOtherSessions,
  forceLogout
} from "../controllers/session.controller";
import { authorize } from "../middleware/authorization";

const sessionRoutes = express.Router();

// Get all active sessions for the current user
sessionRoutes.get("/", authorize as <PERSON>quest<PERSON>and<PERSON>, getUserSessions as RequestHandler);

// Revoke a specific session (remote logout)
sessionRoutes.post("/revoke", authorize as RequestHand<PERSON>, revokeUserSession as RequestHandler);

// Revoke multiple sessions
sessionRoutes.post("/revoke-multiple", authorize as RequestHand<PERSON>, revokeMultipleSessions as Request<PERSON>andler);

// Revoke all other sessions (keep current session active)
sessionRoutes.post("/revoke-all-others", authorize as RequestHand<PERSON>, revokeAllOtherSessions as RequestHandler);

// Force logout (for when session limit is exceeded) - doesn't require authentication
sessionRoutes.post("/force-logout", forceLogout as RequestHandler);

export default sessionRoutes;
