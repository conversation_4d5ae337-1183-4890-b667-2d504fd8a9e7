import express, { RequestHand<PERSON> } from "express";
import { 
  getUserSessions, 
  revokeUserSession, 
  revokeMultipleSessions,
  revokeAllOtherSessions,
  forceLogout,
  cleanupSessions 
} from "../controllers/session.controller";
import { authorize, authorizeAdmin, authorizeWithSession } from "../middleware/authorization";

const sessionRoutes = express.Router();

// Get all active sessions for the current user
sessionRoutes.get("/", authorize as RequestHandler, getUserSessions as RequestHandler);

// Revoke a specific session (remote logout)
sessionRoutes.post("/revoke", authorize as RequestHandler, revokeUserSession as Request<PERSON>and<PERSON>);

// Revoke multiple sessions
sessionRoutes.post("/revoke-multiple", authorize as RequestHand<PERSON>, revokeMultipleSessions as RequestHandler);

// Revoke all other sessions (keep current session active)
sessionRoutes.post("/revoke-all-others", authorize as RequestHand<PERSON>, revokeAllOtherSessions as RequestHandler);

// Force logout (for when session limit is exceeded) - doesn't require authentication
sessionRoutes.post("/force-logout", forceLogout as RequestHandler);

// Admin endpoint to cleanup expired sessions
sessionRoutes.post("/cleanup", authorize as RequestHandler, authorizeAdmin as RequestHandler, cleanupSessions as RequestHandler);

export default sessionRoutes;
