import express, { Request<PERSON><PERSON><PERSON> } from "express";
import {
  getUserSessions,
  revokeUserSession,
  revokeMultipleSessions,
  revokeAllOtherSessions,
  forceLogout
} from "../controllers/session.controller";
import { authorize, authorizeWithSession } from "../middleware/authorization";

const sessionRoutes = express.Router();

// Get all active sessions for the current user
sessionRoutes.get("/", authorizeWithSession as RequestHandler, getUserSessions as RequestHandler);

// Revoke a specific session (remote logout)
sessionRoutes.post("/revoke", authorizeWithSession as RequestHandler, revokeUserSession as RequestHandler);

// Revoke multiple sessions
sessionRoutes.post("/revoke-multiple", authorizeWithSession as RequestHandler, revokeMultipleSessions as RequestHandler);

// Revoke all other sessions (keep current session active)
sessionRoutes.post("/revoke-all-others", authorizeWithSession as RequestHand<PERSON>, revokeAllOtherSessions as RequestHand<PERSON>);

// Force logout (for when session limit is exceeded) - doesn't require authentication
sessionRoutes.post("/force-logout", forceLogout as RequestHandler);

export default sessionRoutes;
