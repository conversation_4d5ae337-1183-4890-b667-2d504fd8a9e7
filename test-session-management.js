#!/usr/bin/env node

/**
 * Session Management Testing Script
 * 
 * This script tests the device/session management system by:
 * 1. Creating a test user
 * 2. Simulating logins from multiple devices
 * 3. Testing session limit enforcement
 * 4. Testing remote logout functionality
 * 5. Testing session cleanup
 */

const axios = require('axios');
const colors = require('colors');

// Configuration
const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  name: 'Session Test User',
  email: '<EMAIL>',
  password: 'testpassword123'
};

// Device simulation data
const DEVICES = [
  {
    name: 'iPhone 15',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15',
      browser: 'Safari 17.0',
      os: 'iOS 17.0',
      device: 'iPhone',
      isMobile: true
    }
  },
  {
    name: 'Chrome Desktop',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      browser: 'Chrome 120.0',
      os: 'Windows 10',
      device: 'Desktop',
      isMobile: false
    }
  },
  {
    name: 'MacBook Safari',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
      browser: 'Safari 17.1',
      os: 'macOS 10.15.7',
      device: 'Desktop',
      isMobile: false
    }
  },
  {
    name: 'Android Phone',
    userAgent: 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36',
      browser: 'Chrome 120.0',
      os: 'Android 14',
      device: 'SM-G998B',
      isMobile: true
    }
  }
];

// Helper functions
const log = {
  info: (msg) => console.log('ℹ️ '.blue + msg),
  success: (msg) => console.log('✅ '.green + msg.green),
  error: (msg) => console.log('❌ '.red + msg.red),
  warning: (msg) => console.log('⚠️ '.yellow + msg.yellow),
  step: (step, msg) => console.log(`\n${'Step ' + step + ':'.cyan.bold} ${msg}`)
};

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// API helper functions
async function makeRequest(method, endpoint, data = null, headers = {}) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      withCredentials: true
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
}

async function registerUser() {
  log.step(1, 'Registering test user');
  
  const result = await makeRequest('POST', '/v0/auth/signup', TEST_USER);
  
  if (result.success) {
    log.success('User registered successfully');
    return true;
  } else if (result.status === 400 && result.error.error?.includes('already exists')) {
    log.warning('User already exists, continuing...');
    return true;
  } else {
    log.error(`Registration failed: ${JSON.stringify(result.error)}`);
    return false;
  }
}

async function loginFromDevice(device, expectSuccess = true) {
  log.info(`Attempting login from ${device.name}...`);
  
  const loginData = {
    email: TEST_USER.email,
    password: TEST_USER.password,
    deviceInfo: device.deviceInfo
  };

  const headers = {
    'User-Agent': device.userAgent
  };

  const result = await makeRequest('POST', '/v0/auth/signin', loginData, headers);
  
  if (expectSuccess && result.success) {
    log.success(`✓ Login successful from ${device.name}`);
    log.info(`  Session ID: ${result.data.sessionId}`);
    return { success: true, sessionId: result.data.sessionId, cookies: result.headers?.['set-cookie'] };
  } else if (!expectSuccess && result.status === 409) {
    log.warning(`✓ Session limit exceeded as expected from ${device.name}`);
    log.info(`  Active sessions: ${result.error.activeSessions?.length || 0}`);
    return { success: false, sessionLimitExceeded: true, activeSessions: result.error.activeSessions };
  } else {
    log.error(`✗ Login failed from ${device.name}: ${JSON.stringify(result.error)}`);
    return { success: false };
  }
}

async function getSessions(cookies) {
  log.info('Fetching active sessions...');
  
  const headers = cookies ? { 'Cookie': cookies.join('; ') } : {};
  const result = await makeRequest('GET', '/v0/sessions', null, headers);
  
  if (result.success) {
    log.success(`Found ${result.data.sessions.length} active sessions`);
    result.data.sessions.forEach((session, index) => {
      log.info(`  ${index + 1}. ${session.deviceInfo.browser} on ${session.deviceInfo.os} (${session.sessionId.substring(0, 8)}...)`);
    });
    return result.data.sessions;
  } else {
    log.error(`Failed to fetch sessions: ${JSON.stringify(result.error)}`);
    return [];
  }
}

async function revokeSession(sessionId, cookies) {
  log.info(`Revoking session ${sessionId.substring(0, 8)}...`);
  
  const headers = cookies ? { 'Cookie': cookies.join('; ') } : {};
  const result = await makeRequest('POST', '/v0/sessions/revoke', { sessionId }, headers);
  
  if (result.success) {
    log.success('Session revoked successfully');
    return true;
  } else {
    log.error(`Failed to revoke session: ${JSON.stringify(result.error)}`);
    return false;
  }
}

async function forceLogout(sessionId) {
  log.info(`Force logout session ${sessionId.substring(0, 8)}...`);
  
  const result = await makeRequest('POST', '/v0/sessions/force-logout', { sessionToRevoke: sessionId });
  
  if (result.success) {
    log.success('Force logout successful');
    return true;
  } else {
    log.error(`Force logout failed: ${JSON.stringify(result.error)}`);
    return false;
  }
}

// Main testing function
async function runSessionTests() {
  console.log('🧪 Session Management Testing Script'.cyan.bold);
  console.log('=====================================\n');

  try {
    // Step 1: Register user
    const registered = await registerUser();
    if (!registered) {
      log.error('Failed to register user, aborting tests');
      return;
    }

    await sleep(1000);

    // Step 2: Login from first device (should succeed)
    log.step(2, 'Testing login from first device');
    const device1Login = await loginFromDevice(DEVICES[0]);
    if (!device1Login.success) {
      log.error('First device login failed, aborting tests');
      return;
    }

    await sleep(1000);

    // Step 3: Login from second device (should succeed)
    log.step(3, 'Testing login from second device');
    const device2Login = await loginFromDevice(DEVICES[1]);
    if (!device2Login.success) {
      log.error('Second device login failed');
      return;
    }

    await sleep(1000);

    // Step 4: Login from third device (should succeed)
    log.step(4, 'Testing login from third device');
    const device3Login = await loginFromDevice(DEVICES[2]);
    if (!device3Login.success) {
      log.error('Third device login failed');
      return;
    }

    await sleep(1000);

    // Step 5: Check active sessions
    log.step(5, 'Checking active sessions');
    const sessions = await getSessions(device1Login.cookies);
    if (sessions.length !== 3) {
      log.warning(`Expected 3 sessions, found ${sessions.length}`);
    }

    await sleep(1000);

    // Step 6: Try login from fourth device (should fail with session limit)
    log.step(6, 'Testing session limit enforcement (4th device)');
    const device4Login = await loginFromDevice(DEVICES[3], false);
    if (!device4Login.sessionLimitExceeded) {
      log.error('Session limit was not enforced!');
      return;
    }

    await sleep(1000);

    // Step 7: Force logout one session and retry
    log.step(7, 'Testing force logout and retry login');
    if (device4Login.activeSessions && device4Login.activeSessions.length > 0) {
      const sessionToRevoke = device4Login.activeSessions[0].sessionId;
      const forceLogoutSuccess = await forceLogout(sessionToRevoke);
      
      if (forceLogoutSuccess) {
        await sleep(1000);
        log.info('Retrying login from 4th device...');
        const retryLogin = await loginFromDevice(DEVICES[3]);
        if (retryLogin.success) {
          log.success('Login successful after force logout!');
        } else {
          log.error('Login still failed after force logout');
        }
      }
    }

    await sleep(1000);

    // Step 8: Test remote logout
    log.step(8, 'Testing remote logout functionality');
    const updatedSessions = await getSessions(device1Login.cookies);
    if (updatedSessions.length > 1) {
      const sessionToRevoke = updatedSessions.find(s => !s.isCurrent);
      if (sessionToRevoke) {
        await revokeSession(sessionToRevoke.sessionId, device1Login.cookies);
      }
    }

    await sleep(1000);

    // Step 9: Final session check
    log.step(9, 'Final session verification');
    const finalSessions = await getSessions(device1Login.cookies);
    log.info(`Final active sessions: ${finalSessions.length}`);
    log.info('Note: Expired sessions are automatically cleaned up by MongoDB TTL');

    console.log('\n🎉 Session Management Tests Completed!'.green.bold);
    console.log('=====================================');
    console.log('✅ Session limit enforcement: Working');
    console.log('✅ Force logout mechanism: Working');
    console.log('✅ Remote session management: Working');
    console.log('✅ Automatic cleanup: Handled by MongoDB TTL');

  } catch (error) {
    log.error(`Test execution failed: ${error.message}`);
    console.error(error);
  }
}

// Check if axios is available
try {
  require('axios');
  require('colors');
} catch (error) {
  console.error('❌ Missing dependencies. Please install:');
  console.error('npm install axios colors');
  process.exit(1);
}

// Run the tests
if (require.main === module) {
  runSessionTests().catch(console.error);
}

module.exports = { runSessionTests };
