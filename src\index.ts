import express, { Request, Response } from "express";
import cors from "cors";
import dotenv from "dotenv";
import cookieParser from "cookie-parser";
import { DBconnection } from "./config/db";
import { errorHandler } from "./middleware/errorHandler";
import { notFound } from "./middleware/notFound";
import userRoutes from "./routes/user.routes";
import adminRoutes from "./routes/admin.routes";
import sessionRoutes from "./routes/session.routes";
import { startSessionCleanup, gracefulShutdown } from "./utils/sessionCleanup";

dotenv.config();

const app = express();
const PORT = process.env.PORT ?? 3000;
DBconnection();
app.use(cors());
app.use(express.json());
app.use(cookieParser());
//
app.get("/test", (_, res: Response) => {
  res.send("Hello From Server");
});
//
app.use("/v0/auth", userRoutes);
app.use("/v0/admin", adminRoutes);
app.use("/v0/sessions", sessionRoutes);
app.use(notFound);
app.use(errorHandler);

// Start session cleanup
startSessionCleanup();

// Graceful shutdown handlers
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

app.listen(PORT, () => {
  console.log(`Server Running on Port ${PORT}`);
});
