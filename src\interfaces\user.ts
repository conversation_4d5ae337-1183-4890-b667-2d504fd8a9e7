
export type User = {
    name: string;
    email: string;
    password: string;
    profileImage: string;
    role: string;
    status: string;
};

export enum Role {
    User = "user",
    Admin = "admin",
  }

export enum Status {
    Active = "activated",
    Inactive = "inactivated",
    Suspended = "suspended",
    Banned = "banned",
  }

// Session-related interfaces
export interface DeviceInfo {
    userAgent: string;
    browser: string;
    os: string;
    device: string;
    isMobile: boolean;
}

export interface SessionLocation {
    ip: string;
    country?: string;
    city?: string;
    timezone?: string;
}

export interface UserSession {
    _id?: string;
    userId: string;
    sessionId: string;
    deviceInfo: DeviceInfo;
    location: SessionLocation;
    accessTokenHash: string;
    refreshTokenHash: string;
    createdAt: Date;
    lastActiveAt: Date;
    expiresAt: Date;
}
  