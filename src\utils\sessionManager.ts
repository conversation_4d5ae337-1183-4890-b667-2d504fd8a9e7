import crypto from 'crypto';
import { Request } from 'express';
import UAParser from 'ua-parser-js';
import UserSessionModel from '../models/UserSession';
import { DeviceInfo, SessionLocation, SessionStatus, UserSession } from '../interfaces/user';

// Generate unique session ID
export const generateSessionId = (): string => {
  return crypto.randomBytes(32).toString('hex');
};

// Create hash for token storage (for security)
export const createTokenHash = (token: string): string => {
  return crypto.createHash('sha256').update(token).digest('hex');
};

// Extract device information from request
export const extractDeviceInfo = (req: Request): DeviceInfo => {
  const userAgent = req.headers['user-agent'] || '';
  const parser = new UAParser(userAgent);
  const result = parser.getResult();

  return {
    userAgent,
    browser: `${result.browser.name || 'Unknown'} ${result.browser.version || ''}`.trim(),
    os: `${result.os.name || 'Unknown'} ${result.os.version || ''}`.trim(),
    device: result.device.model || result.device.vendor || 'Desktop',
    isMobile: result.device.type === 'mobile' || result.device.type === 'tablet' || false,
  };
};

// Extract location information from request
export const extractLocationInfo = (req: Request): SessionLocation => {
  // Get IP address (considering proxy headers)
  const ip = (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
             req.headers['x-real-ip'] as string ||
             req.connection.remoteAddress ||
             req.socket.remoteAddress ||
             '127.0.0.1';

  return {
    ip: ip.replace('::ffff:', ''), // Remove IPv6 prefix if present
    country: req.headers['cf-ipcountry'] as string || '', // Cloudflare header
    city: req.headers['cf-ipcity'] as string || '', // Cloudflare header
    timezone: req.headers['cf-timezone'] as string || '', // Cloudflare header
  };
};

// Calculate session expiration time
export const calculateSessionExpiration = (): Date => {
  const expirationTime = process.env.SESSION_EXPIRATION_TIME || '7d';
  const now = new Date();
  
  // Parse time string (e.g., "7d", "24h", "30m")
  const timeMatch = expirationTime.match(/^(\d+)([dhm])$/);
  if (!timeMatch) {
    // Default to 7 days if invalid format
    return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  }

  const [, amount, unit] = timeMatch;
  const amountNum = parseInt(amount, 10);

  switch (unit) {
    case 'd': // days
      return new Date(now.getTime() + amountNum * 24 * 60 * 60 * 1000);
    case 'h': // hours
      return new Date(now.getTime() + amountNum * 60 * 60 * 1000);
    case 'm': // minutes
      return new Date(now.getTime() + amountNum * 60 * 1000);
    default:
      return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  }
};

// Create new session
export const createSession = async (
  userId: string,
  accessToken: string,
  refreshToken: string,
  req: Request
): Promise<UserSession> => {
  const sessionId = generateSessionId();
  const deviceInfo = extractDeviceInfo(req);
  const location = extractLocationInfo(req);
  const expiresAt = calculateSessionExpiration();

  const sessionData: Partial<UserSession> = {
    userId,
    sessionId,
    deviceInfo,
    location,
    status: SessionStatus.Active,
    accessTokenHash: createTokenHash(accessToken),
    refreshTokenHash: createTokenHash(refreshToken),
    createdAt: new Date(),
    lastActiveAt: new Date(),
    expiresAt,
  };

  const session = new UserSessionModel(sessionData);
  await session.save();
  
  return session.toObject();
};

// Validate session by token hash
export const validateSessionByToken = async (
  accessToken: string,
  userId: string
): Promise<UserSession | null> => {
  const tokenHash = createTokenHash(accessToken);
  
  const session = await UserSessionModel.findOne({
    userId,
    accessTokenHash: tokenHash,
    status: SessionStatus.Active,
    expiresAt: { $gt: new Date() }
  });

  if (session) {
    // Update last active time
    session.lastActiveAt = new Date();
    await session.save();
    return session.toObject();
  }

  return null;
};

// Get active sessions count for user
export const getActiveSessionsCount = async (userId: string): Promise<number> => {
  return await UserSessionModel.countActiveSessions(userId);
};

// Get active sessions for user
export const getActiveSessions = async (userId: string): Promise<UserSession[]> => {
  const sessions = await UserSessionModel.findActiveSessions(userId);
  return sessions.map(session => session.toObject());
};

// Revoke session
export const revokeSession = async (sessionId: string): Promise<boolean> => {
  const result = await UserSessionModel.revokeSession(sessionId);
  return !!result;
};

// Revoke all user sessions except current
export const revokeAllUserSessions = async (
  userId: string,
  excludeSessionId?: string
): Promise<number> => {
  const result = await UserSessionModel.revokeAllUserSessions(userId, excludeSessionId);
  return result.modifiedCount;
};

// Clean up expired sessions
export const cleanupExpiredSessions = async (): Promise<number> => {
  const result = await UserSessionModel.cleanupExpiredSessions();
  return result.deletedCount;
};

// Check if session limit is exceeded
export const isSessionLimitExceeded = async (userId: string): Promise<boolean> => {
  const maxSessions = parseInt(process.env.MAX_CONCURRENT_SESSIONS || '3', 10);
  const activeCount = await getActiveSessionsCount(userId);
  return activeCount >= maxSessions;
};

// Get oldest session for forced logout
export const getOldestSession = async (userId: string): Promise<UserSession | null> => {
  const session = await UserSessionModel.findOne({
    userId,
    status: SessionStatus.Active,
    expiresAt: { $gt: new Date() }
  }).sort({ lastActiveAt: 1 }); // Oldest first

  return session ? session.toObject() : null;
};

// Update session tokens (for refresh token flow)
export const updateSessionTokens = async (
  sessionId: string,
  newAccessToken: string,
  newRefreshToken?: string
): Promise<boolean> => {
  const updateData: any = {
    accessTokenHash: createTokenHash(newAccessToken),
    lastActiveAt: new Date(),
  };

  if (newRefreshToken) {
    updateData.refreshTokenHash = createTokenHash(newRefreshToken);
  }

  const result = await UserSessionModel.findOneAndUpdate(
    { sessionId, status: SessionStatus.Active },
    updateData,
    { new: true }
  );

  return !!result;
};

// Get session by session ID
export const getSessionById = async (sessionId: string): Promise<UserSession | null> => {
  const session = await UserSessionModel.findOne({ sessionId });
  return session ? session.toObject() : null;
};
