import mongoose, { Schema, Document } from "mongoose";
import { UserSession, DeviceInfo, SessionLocation, SessionStatus } from "../interfaces/user";

// Extend UserSession interface with Document for Mongoose
export interface UserSessionDocument extends UserSession, Document {}

// Device Info Schema
const deviceInfoSchema = new Schema<DeviceInfo>({
  userAgent: { type: String, required: true },
  browser: { type: String, required: true },
  os: { type: String, required: true },
  device: { type: String, required: true },
  isMobile: { type: Boolean, required: true, default: false },
}, { _id: false });

// Session Location Schema
const sessionLocationSchema = new Schema<SessionLocation>({
  ip: { type: String, required: true },
  country: { type: String, default: "" },
  city: { type: String, default: "" },
  timezone: { type: String, default: "" },
}, { _id: false });

// User Session Schema
const userSessionSchema = new Schema<UserSessionDocument>({
  userId: { 
    type: String, 
    required: true, 
    ref: "User",
    index: true 
  },
  sessionId: { 
    type: String, 
    required: true, 
    unique: true,
    index: true 
  },
  deviceInfo: { 
    type: deviceInfoSchema, 
    required: true 
  },
  location: { 
    type: sessionLocationSchema, 
    required: true 
  },
  status: { 
    type: String, 
    enum: Object.values(SessionStatus), 
    default: SessionStatus.Active,
    index: true 
  },
  accessTokenHash: { 
    type: String, 
    required: true 
  },
  refreshTokenHash: { 
    type: String, 
    required: true 
  },
  createdAt: { 
    type: Date, 
    default: Date.now,
    index: true 
  },
  lastActiveAt: { 
    type: Date, 
    default: Date.now,
    index: true 
  },
  expiresAt: { 
    type: Date, 
    required: true,
    index: true 
  },
}, {
  versionKey: false,
  timestamps: false // We're managing timestamps manually
});

// Compound indexes for efficient queries
userSessionSchema.index({ userId: 1, status: 1 });
userSessionSchema.index({ userId: 1, createdAt: -1 });
userSessionSchema.index({ status: 1, expiresAt: 1 });
userSessionSchema.index({ sessionId: 1, status: 1 });

// TTL index to automatically remove expired sessions
userSessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Instance methods
userSessionSchema.methods.isExpired = function(): boolean {
  return this.expiresAt < new Date() || this.status !== SessionStatus.Active;
};

userSessionSchema.methods.updateLastActive = function(): void {
  this.lastActiveAt = new Date();
};

userSessionSchema.methods.revoke = function(): void {
  this.status = SessionStatus.Revoked;
};

// Static methods
userSessionSchema.statics.findActiveSessions = function(userId: string) {
  return this.find({ 
    userId, 
    status: SessionStatus.Active,
    expiresAt: { $gt: new Date() }
  }).sort({ lastActiveAt: -1 });
};

userSessionSchema.statics.countActiveSessions = function(userId: string) {
  return this.countDocuments({ 
    userId, 
    status: SessionStatus.Active,
    expiresAt: { $gt: new Date() }
  });
};

userSessionSchema.statics.revokeSession = function(sessionId: string) {
  return this.findOneAndUpdate(
    { sessionId, status: SessionStatus.Active },
    { status: SessionStatus.Revoked },
    { new: true }
  );
};

userSessionSchema.statics.revokeAllUserSessions = function(userId: string, excludeSessionId?: string) {
  const query: any = { userId, status: SessionStatus.Active };
  if (excludeSessionId) {
    query.sessionId = { $ne: excludeSessionId };
  }
  return this.updateMany(query, { status: SessionStatus.Revoked });
};

userSessionSchema.statics.cleanupExpiredSessions = function() {
  return this.deleteMany({
    $or: [
      { expiresAt: { $lt: new Date() } },
      { status: { $ne: SessionStatus.Active } }
    ]
  });
};

const UserSessionModel = mongoose.model<UserSessionDocument>("UserSession", userSessionSchema);

export default UserSessionModel;
