import mongoose, { Schema, Document } from "mongoose";
import { UserSession, DeviceInfo, SessionLocation } from "../interfaces/user";

// Extend UserSession interface with Document for Mongoose
export interface UserSessionDocument extends Omit<UserSession, '_id'>, Document {}

// Device Info Schema
const deviceInfoSchema = new Schema<DeviceInfo>({
  userAgent: { type: String, required: true },
  browser: { type: String, required: true },
  os: { type: String, required: true },
  device: { type: String, required: true },
  isMobile: { type: Boolean, required: true, default: false },
}, { _id: false });

// Session Location Schema
const sessionLocationSchema = new Schema<SessionLocation>({
  ip: { type: String, required: true },
  country: { type: String, default: "" },
  city: { type: String, default: "" },
  timezone: { type: String, default: "" },
}, { _id: false });

// User Session Schema
const userSessionSchema = new Schema<UserSessionDocument>({
  userId: { 
    type: String, 
    required: true, 
    ref: "User",
    index: true 
  },
  sessionId: { 
    type: String, 
    required: true, 
    unique: true,
    index: true 
  },
  deviceInfo: { 
    type: deviceInfoSchema, 
    required: true 
  },
  location: { 
    type: sessionLocationSchema, 
    required: true 
  },

  accessTokenHash: { 
    type: String, 
    required: true 
  },
  refreshTokenHash: { 
    type: String, 
    required: true 
  },
  createdAt: { 
    type: Date, 
    default: Date.now,
    index: true 
  },
  lastActiveAt: { 
    type: Date, 
    default: Date.now,
    index: true 
  },
  expiresAt: { 
    type: Date, 
    required: true,
    index: true 
  },
}, {
  versionKey: false,
  timestamps: false // We're managing timestamps manually
});

// Compound indexes for efficient queries
userSessionSchema.index({ userId: 1, createdAt: -1 });
userSessionSchema.index({ sessionId: 1 });

// TTL index to automatically remove expired sessions
userSessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });


// Static methods
userSessionSchema.statics.findActiveSessions = function(userId: string) {
  return this.find({
    userId,
    expiresAt: { $gt: new Date() }
  }).sort({ lastActiveAt: -1 });
};

userSessionSchema.statics.countActiveSessions = function(userId: string) {
  return this.countDocuments({
    userId,
    expiresAt: { $gt: new Date() }
  });
};

userSessionSchema.statics.deleteSession = function(sessionId: string) {
  return this.findOneAndDelete({ sessionId });
};

userSessionSchema.statics.deleteAllUserSessions = function(userId: string, excludeSessionId?: string) {
  const query: any = { userId };
  if (excludeSessionId) {
    query.sessionId = { $ne: excludeSessionId };
  }
  return this.deleteMany(query);
};

const UserSessionModel = mongoose.model<UserSessionDocument>("UserSession", userSessionSchema);

export default UserSessionModel;
