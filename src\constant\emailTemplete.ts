export const emailTemplate = (email: string, verificationToken?: string) => {
  const verificationUrl = verificationToken
    ? `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify?token=${encodeURIComponent(verificationToken)}`
    : `${process.env.FRONTEND_URL || 'http://localhost:3000'}/verify?email=${encodeURIComponent(email)}`;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            .logo {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
            .title {
                color: #2c3e50;
                margin-bottom: 20px;
            }
            .content {
                margin-bottom: 30px;
                line-height: 1.8;
            }
            .verify-button {
                display: inline-block;
                background-color: #3498db;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                text-align: center;
                margin: 20px 0;
                transition: background-color 0.3s;
            }
            .verify-button:hover {
                background-color: #2980b9;
            }
            .button-container {
                text-align: center;
                margin: 30px 0;
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                font-size: 14px;
                color: #666;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">Personal Coach</div>
                <h1 class="title">Verify Your Email Address</h1>
            </div>

            <div class="content">
                <p>Hello,</p>
                <p>Thank you for registering with Personal Coach! To complete your registration and start using your account, please verify your email address by clicking the button below.</p>

                <div class="button-container">
                    <a href="${verificationUrl}" class="verify-button">Verify Email Address</a>
                </div>

                <p>If the button above doesn't work, you can also copy and paste the following link into your browser:</p>
                <p style="word-break: break-all; color: #3498db;">${verificationUrl}</p>
            </div>

            <div class="footer">
                <p>Best regards,<br>The Personal Coach Team</p>
                <p style="font-size: 12px; color: #999;">
                    This is an automated email. Please do not reply to this message.
                </p>
            </div>
        </div>
    </body>
    </html>
  `;
};

export const resetPasswordOtpTemplate = (email: string, otp: string) => {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
            }
            .logo {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
            .title {
                color: #2c3e50;
                margin-bottom: 20px;
            }
            .content {
                margin-bottom: 30px;
                line-height: 1.8;
            }
            .otp-box {
                display: block;
                background-color: #3498db;
                color: white;
                padding: 15px 30px;
                border-radius: 5px;
                font-size: 28px;
                font-weight: bold;
                letter-spacing: 8px;
                margin: 20px auto;
                text-align: center;
            }
            .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                font-size: 14px;
                color: #666;
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">Personal Coach</div>
                <h1 class="title">Reset Your Password</h1>
            </div>

            <div class="content">
                <p>Hello,</p>
                <p>We received a request to reset your password for your Personal Coach account. Please use the OTP below to reset your password. This OTP is valid for 1 minute.</p>
                <div class="otp-box">${otp}</div>
                <p>If you did not request a password reset, you can safely ignore this email.</p>
            </div>

            <div class="footer">
                <p>Best regards,<br>The Personal Coach Team</p>
                <p style="font-size: 12px; color: #999;">
                    This is an automated email. Please do not reply to this message.
                </p>
            </div>
        </div>
    </body>
    </html>
  `;
};