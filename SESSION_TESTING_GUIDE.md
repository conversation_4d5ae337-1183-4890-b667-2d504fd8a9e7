# Session Management Testing Guide

This guide provides step-by-step instructions for testing the device/session management system to verify it works correctly.

## 🚀 Quick Start

### Prerequisites
1. Make sure your server is running on `http://localhost:3000`
2. Install required dependencies:
   ```bash
   npm install axios colors
   ```

### Run the Automated Test Script
```bash
node test-session-management.js
```

This script will automatically test all major functionality including session limits, device tracking, and remote logout.

---

## 📋 Manual Testing Scenarios

### Scenario 1: Basic Session Creation and Tracking

**Objective**: Verify that sessions are created and tracked properly

**Steps**:
1. Register a new user via `/v0/auth/signup`
2. Login from different devices using different User-Agent headers
3. Check that each login creates a unique session
4. Verify device information is captured correctly

**Expected Results**:
- Each login creates a new session entry
- Device info (browser, OS, device type) is captured
- Session IDs are unique
- Location info (IP) is recorded

### Scenario 2: Session Limit Enforcement

**Objective**: Verify the 3-device limit is enforced

**Steps**:
1. <PERSON><PERSON> from Device 1 (iPhone) ✅
2. <PERSON><PERSON> from Device 2 (Chrome Desktop) ✅
3. <PERSON><PERSON> from Device 3 (Safari Mac) ✅
4. Attempt login from Device 4 (Android) ❌

**Expected Results**:
- First 3 logins succeed (HTTP 200)
- 4th login fails with HTTP 409 "Session limit exceeded"
- Response includes list of active sessions
- Error message explains the limit

**Test with curl**:
```bash
# Device 1 - iPhone
curl -X POST http://localhost:3000/v0/auth/signin \
  -H "Content-Type: application/json" \
  -H "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Device 2 - Chrome Desktop  
curl -X POST http://localhost:3000/v0/auth/signin \
  -H "Content-Type: application/json" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Device 3 - Safari Mac
curl -X POST http://localhost:3000/v0/auth/signin \
  -H "Content-Type: application/json" \
  -H "User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Device 4 - Android (should fail)
curl -X POST http://localhost:3000/v0/auth/signin \
  -H "Content-Type: application/json" \
  -H "User-Agent: Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### Scenario 3: Force Logout and Retry

**Objective**: Test the forced logout mechanism when session limit is exceeded

**Steps**:
1. Reach session limit (3 active sessions)
2. Attempt 4th login (should fail with session list)
3. Use force logout endpoint to revoke one session
4. Retry 4th login (should now succeed)

**Expected Results**:
- Force logout successfully revokes the specified session
- Retry login succeeds after force logout
- Total active sessions remains at 3

**Test with curl**:
```bash
# After getting session limit error, use force logout
curl -X POST http://localhost:3000/v0/sessions/force-logout \
  -H "Content-Type: application/json" \
  -d '{"sessionToRevoke":"SESSION_ID_FROM_ERROR_RESPONSE"}'

# Then retry login
curl -X POST http://localhost:3000/v0/auth/signin \
  -H "Content-Type: application/json" \
  -H "User-Agent: Mozilla/5.0 (Linux; Android 14; SM-G998B)" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### Scenario 4: Remote Logout (Session Management)

**Objective**: Test remote logout functionality

**Steps**:
1. Login from multiple devices
2. From Device 1, get list of active sessions
3. From Device 1, revoke Device 2's session
4. Verify Device 2 can no longer access protected endpoints

**Expected Results**:
- Session list shows all active sessions with device info
- Remote revocation succeeds
- Revoked session becomes invalid immediately
- Other sessions remain active

**Test with curl**:
```bash
# Get active sessions (with auth cookies)
curl -X GET http://localhost:3000/v0/sessions \
  -H "Cookie: accessToken=YOUR_ACCESS_TOKEN"

# Revoke specific session
curl -X POST http://localhost:3000/v0/sessions/revoke \
  -H "Content-Type: application/json" \
  -H "Cookie: accessToken=YOUR_ACCESS_TOKEN" \
  -d '{"sessionId":"SESSION_ID_TO_REVOKE"}'

# Verify revoked session is invalid
curl -X GET http://localhost:3000/v0/sessions \
  -H "Cookie: accessToken=REVOKED_SESSION_TOKEN"
```

### Scenario 5: Session Cleanup

**Objective**: Test automatic and manual session cleanup

**Steps**:
1. Create sessions and let them expire (or manually expire in DB)
2. Trigger manual cleanup
3. Verify expired sessions are removed

**Expected Results**:
- Expired sessions are automatically cleaned up
- Manual cleanup removes expired sessions
- Active sessions are not affected

---

## 🧪 Using the Test Utilities

### Basic Usage Example

```javascript
const { APIClient, TestScenarios, Utils } = require('./test-utilities');

async function runTest() {
  // Create test user
  const testUser = Utils.generateTestUser();
  
  // Create clients for different devices
  const clients = TestScenarios.createDeviceClients();
  
  // Register user
  await clients[0].register(testUser);
  
  // Test session limit
  const { results } = await TestScenarios.testSessionLimit(testUser);
  
  console.log('Test Results:', results);
}

runTest().catch(console.error);
```

### Advanced Testing

```javascript
const { APIClient, DEVICE_PROFILES } = require('./test-utilities');

async function advancedTest() {
  // Create specific device client
  const iphoneClient = new APIClient().setDevice('iphone');
  const chromeClient = new APIClient().setDevice('chrome_desktop');
  
  // Test login
  await iphoneClient.signIn('<EMAIL>', 'password');
  await chromeClient.signIn('<EMAIL>', 'password');
  
  // Get sessions from iPhone
  const sessions = await iphoneClient.getSessions();
  console.log('Active sessions:', sessions.data.sessions.length);
  
  // Remote logout Chrome session from iPhone
  const chromeSession = sessions.data.sessions.find(s => 
    s.deviceInfo.browser.includes('Chrome')
  );
  
  if (chromeSession) {
    await iphoneClient.revokeSession(chromeSession.sessionId);
    console.log('Chrome session revoked from iPhone');
  }
}
```

---

## 🔍 What to Look For

### Success Indicators
- ✅ Session limit properly enforced at 3 devices
- ✅ Device information accurately captured
- ✅ Remote logout works across devices
- ✅ Force logout allows retry after limit exceeded
- ✅ Sessions are properly invalidated when revoked
- ✅ Automatic cleanup removes expired sessions

### Potential Issues
- ❌ Session limit not enforced (can login from 4+ devices)
- ❌ Device info not captured or incorrect
- ❌ Remote logout doesn't invalidate sessions
- ❌ Force logout doesn't work
- ❌ Sessions remain active after revocation
- ❌ Memory leaks from sessions not being cleaned up

---

## 🐛 Troubleshooting

### Common Issues

1. **"Session limit not enforced"**
   - Check `MAX_CONCURRENT_SESSIONS` environment variable
   - Verify session creation is working in database
   - Check if session validation is enabled in middleware

2. **"Device info not captured"**
   - Verify User-Agent headers are being sent
   - Check if ua-parser-js is installed and working
   - Ensure device info is included in login request

3. **"Remote logout not working"**
   - Check session ID is correct
   - Verify authorization middleware validates sessions
   - Ensure session is properly marked as revoked in database

4. **"Force logout fails"**
   - Check if session belongs to the user
   - Verify session exists and is active
   - Ensure endpoint doesn't require authentication

### Debug Commands

```bash
# Check active sessions in database
mongo your-database
db.usersessions.find({status: "active"}).pretty()

# Check session cleanup logs
tail -f your-app.log | grep "SESSION CLEANUP"

# Test specific endpoint
curl -v http://localhost:3000/v0/sessions/
```

---

## 📊 Expected Test Results

When running the automated test script, you should see output similar to:

```
🧪 Session Management Testing Script
=====================================

ℹ️  Step 1: Registering test user
✅ User registered successfully

ℹ️  Step 2: Testing login from first device
✅ ✓ Login successful from iPhone 15
ℹ️    Session ID: a1b2c3d4e5f6...

ℹ️  Step 3: Testing login from second device
✅ ✓ Login successful from Chrome Desktop
ℹ️    Session ID: f6e5d4c3b2a1...

ℹ️  Step 4: Testing login from third device
✅ ✓ Login successful from Safari MacBook
ℹ️    Session ID: 1a2b3c4d5e6f...

ℹ️  Step 5: Checking active sessions
✅ Found 3 active sessions

ℹ️  Step 6: Testing session limit enforcement (4th device)
⚠️  ✓ Session limit exceeded as expected from Android Phone
ℹ️    Active sessions: 3

ℹ️  Step 7: Testing force logout and retry login
✅ Force logout successful
✅ Login successful after force logout!

🎉 Session Management Tests Completed!
```

This confirms that your session management system is working correctly!
