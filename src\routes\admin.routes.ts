import { createAdmin, getAllUsers, updateUserStatus, deleteUser } from "../controllers/admin.controller";
import { authorize, authorizeAdmin } from "../middleware/authorization";
import express, { RequestHandler } from "express";

const adminRoutes = express.Router();

// using these will apply these middle ware to all admin routes 
// remove it if you think some routes does not require middlewares

adminRoutes.use(authorize, authorizeAdmin);

// All admin routes require both authentication and admin privileges
adminRoutes.post("/create",  createAdmin as RequestHandler);
adminRoutes.get("/users",  getAllUsers as RequestHandler);
adminRoutes.put("/users/status/:userId",  updateUserStatus as RequestHandler);
adminRoutes.delete("/users/delete/:userId",  deleteUser as RequestHandler);

export default adminRoutes;
