import bcrypt from 'bcrypt';
import { NextFunction, Request, Response } from "express";
import userModel from './../models/User';
import { createAdminSchema, updateUserStatusSchema, getUsersQuerySchema } from "../schemaValidation/admin";
import { Status, Role } from '../interfaces/user';


export const createAdmin = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[CREATE ADMIN] Incoming request body:", req.body);
    console.log("[CREATE ADMIN] Requesting admin ID:", req.user?.userId);
    
    // Validate request body
    const result = createAdminSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[CREATE ADMIN] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }
    
    const { name, email, password } = result.data;
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create admin user
    const newAdmin = await userModel.create({
      name,
      email,
      password: hashedPassword,
      role: Role.Admin,
      status: Status.Active, // Admins are activated by default
    });
    
    console.log("[CREATE ADMIN] Admin created successfully:", email);
    
    return res.status(201).json({
      message: "Admin created successfully",
      admin: {
        name: newAdmin.name,
        email: newAdmin.email,
        role: newAdmin.role,
        status: newAdmin.status,
        profileImage: newAdmin.profileImage
      }
    });
    
  } catch (error: any) {
    console.error("[CREATE ADMIN] Error:", error);
    
    // Handle MongoDB duplicate key error for email
    if (error.code === 11000 && error.keyPattern?.email) {
      console.log("[CREATE ADMIN] Duplicate email error:", error.keyValue?.email);
      return res.status(409).json({ error: "Email already exists" });
    }
    
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const getAllUsers = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[GET ALL USERS] Query parameters:", req.query);
    console.log("[GET ALL USERS] Requesting admin ID:", req.user?.userId);
    
    // Validate and parse query parameters
    const queryResult = getUsersQuerySchema.safeParse(req.query);
    if (!queryResult.success) {
      console.log("[GET ALL USERS] Query validation failed:", queryResult.error.issues);
      return res.status(400).json({ error: queryResult.error.issues.map((e) => e.message) });
    }
    
    const { page, limit, search, sortBy, sortOrder, status: statusFilter } = queryResult.data;
    
    // Build search query
    const searchQuery: any = {};
    
    // Add search by name or email
    if (search) {
      searchQuery.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Add status filter
    if (statusFilter) {
      searchQuery.status = statusFilter;
    }
    
    // Build sort object
    const sortOptions: any = {};
    const sortDirection = sortOrder === 'desc' ? -1 : 1;
    sortOptions[sortBy] = sortDirection;
    
    // Calculate skip value for pagination
    const skip = (page - 1) * limit;
    
    // Get total count for pagination metadata
    const totalUsers = await userModel.countDocuments(searchQuery);
    
    // Get users with pagination, search, and sorting
    const users = await userModel
      .find(searchQuery)
      .select('-password') // Exclude password field
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .lean(); // Use lean() for better performance
    
    // Calculate pagination metadata
    const totalPages = Math.ceil(totalUsers / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;
    
    console.log(`[GET ALL USERS] Retrieved ${users.length} users (page ${page}/${totalPages})`);
    
    return res.status(200).json({
      message: "Users retrieved successfully",
      users: users.map(user => ({
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        profileImage: user.profileImage
      })),
      pagination: {
        totalUsers,
        totalPages,
        currentPage: page,
        limit,
        hasNextPage,
        hasPrevPage
      },
      filters: {
        search: search || null,
        status: statusFilter || null,
        sortBy,
        sortOrder
      }
    });
    
  } catch (error: any) {
    console.error("[GET ALL USERS] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const updateUserStatus = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[UPDATE USER STATUS] Incoming request body:", req.body);
    console.log("[UPDATE USER STATUS] Target user ID:", req.params.userId);
    console.log("[UPDATE USER STATUS] Requesting admin ID:", req.user?.userId);
    
    // Validate request body
    const result = updateUserStatusSchema.safeParse(req.body);
    if (!result.success) {
      console.log("[UPDATE USER STATUS] Validation failed:", result.error.issues);
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }
    
    const { status } = result.data;
    const targetUserId = req.params.userId;
    const adminUserId = req.user?.userId;
    
    if (!adminUserId) {
      console.log("[UPDATE USER STATUS] No admin ID found in token");
      return res.status(401).json({ error: "Authentication required" });
    }

    
    // Find the target user
    const targetUser = await userModel.findById(targetUserId);
    if (!targetUser) {
      console.log("[UPDATE USER STATUS] Target user not found:", targetUserId);
      return res.status(404).json({ error: "User not found" });
    }
    
    
    // Update user status
    const updatedUser = await userModel.findByIdAndUpdate(
      targetUserId,
      { status },
      { new: true, runValidators: true }
    ).select('-password');
    
    if (!updatedUser) {
      console.log("[UPDATE USER STATUS] Failed to update user:", targetUserId);
      return res.status(404).json({ error: "User not found" });
    }
    
    console.log(`[UPDATE USER STATUS] User status updated: ${updatedUser.email} -> ${status}`);
    
    return res.status(200).json({
      message: `User status updated to ${status} successfully`,
      user: {
        id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        status: updatedUser.status,
        profileImage: updatedUser.profileImage
      }
    });
    
  } catch (error: any) {
    console.error("[UPDATE USER STATUS] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const deleteUser = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[DELETE USER] Target user ID:", req.params.userId);
    console.log("[DELETE USER] Requesting admin ID:", req.user?.userId);
    
    const targetUserId = req.params.userId;
    const adminUserId = req.user?.userId;
    
    if (!adminUserId) {
      console.log("[DELETE USER] No admin ID found in token");
      return res.status(401).json({ error: "Authentication required" });
    }
    
    // Prevent admin from deleting themselves
    if (targetUserId === adminUserId) {
      console.log("[DELETE USER] Admin attempting to delete own account");
      return res.status(403).json({ error: "Cannot delete your own account" });
    }
    
    // Find the target user first to check if they exist and get their role
    const targetUser = await userModel.findById(targetUserId);
    if (!targetUser) {
      console.log("[DELETE USER] Target user not found:", targetUserId);
      return res.status(404).json({ error: "User not found" });
    }
    
    // Prevent deleting other admin accounts
    if (targetUser.role === Role.Admin) {
      console.log("[DELETE USER] Attempting to delete admin account:", targetUser.email);
      return res.status(403).json({ error: "Cannot delete admin accounts" });
    }
    
    // Delete the user
    const deletedUser = await userModel.findByIdAndDelete(targetUserId);
    
    if (!deletedUser) {
      console.log("[DELETE USER] Failed to delete user:", targetUserId);
      return res.status(404).json({ error: "User not found" });
    }
    
    console.log(`[DELETE USER] User deleted successfully: ${deletedUser.email}`);
    
    return res.status(200).json({
      message: `User ${deletedUser.email} has been permanently deleted`
    });
    
  } catch (error: any) {
    console.error("[DELETE USER] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};
