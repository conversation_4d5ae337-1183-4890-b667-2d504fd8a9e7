
import { register, verifyEmail, signIn, refreshToken, signOut, updateProfile, changePassword, requestPasswordResetOTP, confirmPasswordReset } from "../controllers/user.controller";
import { authorize } from "../middleware/authorization";
import express, { RequestHand<PERSON> } from "express";

const userRoutes = express.Router();



userRoutes.post("/signup", register as <PERSON><PERSON><PERSON>andler);
userRoutes.get("/verify-email/:token", verifyEmail as <PERSON>quest<PERSON>andler);
userRoutes.post("/signin", signIn as RequestHandler);
userRoutes.post("/refresh-token", refreshToken as <PERSON>quest<PERSON>andler);
userRoutes.post("/signout", signOut as RequestHandler);

// Protected routes - require authentication
userRoutes.put("/profile", authorize, updateProfile as <PERSON><PERSON><PERSON>and<PERSON>);
userRoutes.put("/change-password", authorize, changePassword as RequestHandler);

userRoutes.post("/reset-password/request", requestPasswordResetOTP as RequestHandler);
userRoutes.post("/reset-password/confirm", confirmPasswordReset as RequestHandler);


export default userRoutes;