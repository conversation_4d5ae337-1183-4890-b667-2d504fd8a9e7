
import { register, verifyEmail, signIn, refreshToken, signOut, updateProfile, changePassword, requestPasswordResetOTP, confirmPasswordReset } from "../controllers/user.controller";
import { authorize, authorizeWithSession } from "../middleware/authorization";
import express, { RequestHandler } from "express";

const userRoutes = express.Router();



userRoutes.post("/signup", register as RequestHandler);
userRoutes.get("/verify-email/:token", verifyEmail as RequestHandler);
userRoutes.post("/signin", signIn as RequestHandler);
userRoutes.post("/refresh-token", refreshToken as RequestHandler);
userRoutes.post("/signout", signOut as RequestHandler);

// Protected routes - require authentication
userRoutes.get("/profile", authorize, updateProfile as RequestHandler);  // Read profile - basic auth
userRoutes.put("/profile", authorizeWithSession, updateProfile as <PERSON>quest<PERSON>and<PERSON>);  // Update profile - strict session auth
userRoutes.put("/change-password", authorizeWithSession, changePassword as Request<PERSON>andler);  // Change password - strict session auth

userRoutes.post("/reset-password/request", requestPasswordResetOTP as RequestHandler);
userRoutes.post("/reset-password/confirm", confirmPasswordReset as RequestHandler);


export default userRoutes;