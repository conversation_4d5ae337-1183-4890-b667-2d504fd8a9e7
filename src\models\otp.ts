import mongoose, { Schema, Document, Types } from "mongoose";

interface Otp extends Document {
  email: string;
  otp: string;
  createdAt: Date;
  expiresAt: Date;
  createdBy?: Types.ObjectId; // Optional for backward compatibility
}

const otpSchema: Schema<Otp> = new Schema(
  {
    email: { type: String, required: true },
    otp: { type: String, required: true },
    createdAt: {
      type: Date,
      default: Date.now,
      expires: 60, // The document will be automatically deleted after 1 minute
    },
    expiresAt: { type: Date, required: false }, // Optional, for explicit expiry if needed
    createdBy: { type: Schema.Types.ObjectId, ref: "User", required: false }, // Not required for password reset
  },
  {
    versionKey: false,
  }
);

otpSchema.index({ email: 1 });
otpSchema.index({ otp: 1 });
otpSchema.index({ email: 1, otp: 1 });

const otpModel = mongoose.model<Otp>("Otp", otpSchema);

export default otpModel;
