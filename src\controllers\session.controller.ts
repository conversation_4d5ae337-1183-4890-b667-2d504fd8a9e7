import { NextFunction, Request, Response } from "express";
import {
  getActiveSessions,
  deleteSession,
  deleteAllUserSessions,
  getSessionById
} from '../utils/sessionManager';
import { 
  revokeSessionSchema, 
  revokeMultipleSessionsSchema, 
  getSessionsQuerySchema,
  forceLogoutSchema 
} from '../schemaValidation/session';

// Get all active sessions for the current user
export const getUserSessions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[GET SESSIONS] Processing request for user:", req.user?.userId);

    // Validate query parameters
    const queryResult = getSessionsQuerySchema.safeParse(req.query);
    if (!queryResult.success) {
      return res.status(400).json({ 
        error: queryResult.error.issues.map((e) => e.message) 
      });
    }

    const { page = 1, limit = 10 } = queryResult.data;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    // Get active sessions
    let sessions = await getActiveSessions(userId);

    // Note: All sessions returned are active (expired ones are handled by TTL)

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedSessions = sessions.slice(startIndex, endIndex);

    // Format response
    const formattedSessions = paginatedSessions.map(session => ({
      sessionId: session.sessionId,
      deviceInfo: session.deviceInfo,
      location: session.location,
      createdAt: session.createdAt,
      lastActiveAt: session.lastActiveAt,
      expiresAt: session.expiresAt,
      isCurrent: session.sessionId === req.user?.sessionId
    }));

    console.log("[GET SESSIONS] Retrieved sessions for user:", userId);

    return res.status(200).json({
      sessions: formattedSessions,
      pagination: {
        page,
        limit,
        total: sessions.length,
        totalPages: Math.ceil(sessions.length / limit)
      }
    });

  } catch (error: any) {
    console.error("[GET SESSIONS] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

// Revoke a specific session (remote logout)
export const revokeUserSession = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[REVOKE SESSION] Processing request for user:", req.user?.userId);

    // Validate request body
    const result = revokeSessionSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ 
        error: result.error.issues.map((e) => e.message) 
      });
    }

    const { sessionId } = result.data;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    // Check if session belongs to the user
    const session = await getSessionById(sessionId);
    if (!session) {
      return res.status(404).json({ error: "Session not found" });
    }

    if (session.userId !== userId) {
      return res.status(403).json({ error: "You can only revoke your own sessions" });
    }

    // Prevent user from revoking their current session
    if (sessionId === req.user?.sessionId) {
      return res.status(400).json({ 
        error: "Cannot revoke current session. Use signout endpoint instead." 
      });
    }

    // Delete the session
    const deleted = await deleteSession(sessionId);
    if (!deleted) {
      return res.status(404).json({ error: "Session not found" });
    }

    console.log("[REVOKE SESSION] Session deleted:", sessionId);

    return res.status(200).json({
      message: "Session revoked successfully",
      sessionId
    });

  } catch (error: any) {
    console.error("[REVOKE SESSION] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

// Revoke multiple sessions
export const revokeMultipleSessions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[REVOKE MULTIPLE SESSIONS] Processing request for user:", req.user?.userId);

    // Validate request body
    const result = revokeMultipleSessionsSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ 
        error: result.error.issues.map((e) => e.message) 
      });
    }

    const { sessionIds } = result.data;
    const userId = req.user?.userId;
    const currentSessionId = req.user?.sessionId;

    if (!userId) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    // Check if any of the sessions is the current session
    if (sessionIds.includes(currentSessionId)) {
      return res.status(400).json({ 
        error: "Cannot revoke current session. Use signout endpoint instead." 
      });
    }

    const results = [];
    for (const sessionId of sessionIds) {
      try {
        // Check if session belongs to the user
        const session = await getSessionById(sessionId);
        if (!session || session.userId !== userId) {
          results.push({ sessionId, success: false, error: "Session not found or unauthorized" });
          continue;
        }

        const deleted = await deleteSession(sessionId);
        results.push({ sessionId, success: deleted });
      } catch (error: any) {
        results.push({ sessionId, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log("[REVOKE MULTIPLE SESSIONS] Revoked sessions:", successCount);

    return res.status(200).json({
      message: `${successCount} of ${sessionIds.length} sessions revoked successfully`,
      results
    });

  } catch (error: any) {
    console.error("[REVOKE MULTIPLE SESSIONS] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

// Revoke all other sessions (keep current session active)
export const revokeAllOtherSessions = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[REVOKE ALL OTHER SESSIONS] Processing request for user:", req.user?.userId);

    const userId = req.user?.userId;
    const currentSessionId = req.user?.sessionId;

    if (!userId) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    // Delete all sessions except current
    const deletedCount = await deleteAllUserSessions(userId, currentSessionId);

    console.log("[REVOKE ALL OTHER SESSIONS] Deleted sessions count:", deletedCount);

    return res.status(200).json({
      message: `${deletedCount} sessions deleted successfully`,
      deletedCount
    });

  } catch (error: any) {
    console.error("[REVOKE ALL OTHER SESSIONS] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

// Force logout (for when session limit is exceeded)
export const forceLogout = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log("[FORCE LOGOUT] Processing request for user:", req.user?.userId);

    // Validate request body
    const result = forceLogoutSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ 
        error: result.error.issues.map((e) => e.message) 
      });
    }

    const { sessionToRevoke } = result.data;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    // Check if session belongs to the user
    const session = await getSessionById(sessionToRevoke);
    if (!session || session.userId !== userId) {
      return res.status(404).json({ error: "Session not found or unauthorized" });
    }

    // Delete the specified session
    const deleted = await deleteSession(sessionToRevoke);
    if (!deleted) {
      return res.status(404).json({ error: "Session not found" });
    }

    console.log("[FORCE LOGOUT] Session force-deleted:", sessionToRevoke);

    return res.status(200).json({
      message: "Session revoked successfully. You can now log in.",
      sessionId: sessionToRevoke
    });

  } catch (error: any) {
    console.error("[FORCE LOGOUT] Error:", error);
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};


