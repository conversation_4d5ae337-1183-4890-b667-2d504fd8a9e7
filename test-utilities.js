/**
 * API Testing Utilities for Session Management
 * 
 * This module provides helper functions for testing the session management system
 */

const axios = require('axios');

// Configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';

/**
 * Device profiles for testing different user agents and device types
 */
const DEVICE_PROFILES = {
  iphone: {
    name: 'iPhone 15 Pro',
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15',
      browser: 'Safari 17.0',
      os: 'iOS 17.0',
      device: 'iPhone',
      isMobile: true
    }
  },
  chrome_desktop: {
    name: 'Chrome Desktop',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      browser: 'Chrome 120.0',
      os: 'Windows 10',
      device: 'Desktop',
      isMobile: false
    }
  },
  safari_mac: {
    name: 'Safari MacBook',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
      browser: 'Safari 17.1',
      os: 'macOS 10.15.7',
      device: 'MacBook',
      isMobile: false
    }
  },
  android: {
    name: 'Android Chrome',
    userAgent: 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36',
      browser: 'Chrome 120.0',
      os: 'Android 14',
      device: 'Samsung Galaxy S24',
      isMobile: true
    }
  },
  firefox: {
    name: 'Firefox Desktop',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    deviceInfo: {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
      browser: 'Firefox 120.0',
      os: 'Windows 10',
      device: 'Desktop',
      isMobile: false
    }
  }
};

/**
 * API Client class for making authenticated requests
 */
class APIClient {
  constructor(baseURL = BASE_URL) {
    this.baseURL = baseURL;
    this.cookies = [];
    this.sessionId = null;
    this.device = null;
  }

  /**
   * Set device profile for this client
   */
  setDevice(deviceKey) {
    this.device = DEVICE_PROFILES[deviceKey];
    return this;
  }

  /**
   * Make HTTP request with proper headers and cookie handling
   */
  async request(method, endpoint, data = null, additionalHeaders = {}) {
    try {
      const headers = {
        'Content-Type': 'application/json',
        ...additionalHeaders
      };

      // Add device user agent if available
      if (this.device) {
        headers['User-Agent'] = this.device.userAgent;
      }

      // Add cookies if available
      if (this.cookies.length > 0) {
        headers['Cookie'] = this.cookies.join('; ');
      }

      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        headers,
        validateStatus: () => true // Don't throw on HTTP errors
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);

      // Store cookies from response
      if (response.headers['set-cookie']) {
        this.cookies = response.headers['set-cookie'];
      }

      return {
        success: response.status >= 200 && response.status < 300,
        status: response.status,
        data: response.data,
        headers: response.headers
      };
    } catch (error) {
      return {
        success: false,
        status: 500,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * Register a new user
   */
  async register(userData) {
    return this.request('POST', '/v0/auth/signup', userData);
  }

  /**
   * Sign in user with device info
   */
  async signIn(email, password) {
    const loginData = { email, password };
    
    // Add device info if device is set
    if (this.device) {
      loginData.deviceInfo = this.device.deviceInfo;
    }

    const result = await this.request('POST', '/v0/auth/signin', loginData);
    
    // Store session ID if login successful
    if (result.success && result.data.sessionId) {
      this.sessionId = result.data.sessionId;
    }

    return result;
  }

  /**
   * Sign out current session
   */
  async signOut() {
    const result = await this.request('POST', '/v0/auth/signout');
    if (result.success) {
      this.cookies = [];
      this.sessionId = null;
    }
    return result;
  }

  /**
   * Get active sessions
   */
  async getSessions() {
    return this.request('GET', '/v0/sessions');
  }

  /**
   * Delete a specific session
   */
  async revokeSession(sessionId) {
    return this.request('POST', '/v0/sessions/revoke', { sessionId });
  }

  /**
   * Revoke multiple sessions
   */
  async revokeMultipleSessions(sessionIds) {
    return this.request('POST', '/v0/sessions/revoke-multiple', { sessionIds });
  }

  /**
   * Revoke all other sessions
   */
  async revokeAllOtherSessions() {
    return this.request('POST', '/v0/sessions/revoke-all-others');
  }

  /**
   * Force logout (doesn't require authentication)
   */
  async forceLogout(sessionId) {
    return this.request('POST', '/v0/sessions/force-logout', { sessionToRevoke: sessionId });
  }

  /**
   * Get current session info
   */
  getSessionInfo() {
    return {
      sessionId: this.sessionId,
      device: this.device?.name,
      hasCookies: this.cookies.length > 0
    };
  }
}

/**
 * Test scenario helper functions
 */
const TestScenarios = {
  /**
   * Create multiple clients with different devices
   */
  createDeviceClients(deviceKeys = ['iphone', 'chrome_desktop', 'safari_mac', 'android']) {
    return deviceKeys.map(key => new APIClient().setDevice(key));
  },

  /**
   * Test session limit enforcement
   */
  async testSessionLimit(userCredentials, maxSessions = 3) {
    const clients = this.createDeviceClients();
    const results = [];

    console.log(`Testing session limit with ${clients.length} devices (limit: ${maxSessions})`);

    for (let i = 0; i < clients.length; i++) {
      const client = clients[i];
      const deviceName = client.device.name;
      
      console.log(`Attempting login ${i + 1}/${clients.length} from ${deviceName}...`);
      
      const result = await client.signIn(userCredentials.email, userCredentials.password);
      
      results.push({
        device: deviceName,
        loginAttempt: i + 1,
        success: result.success,
        status: result.status,
        sessionId: result.data?.sessionId,
        sessionLimitExceeded: result.status === 409,
        activeSessions: result.data?.activeSessions?.length || 0
      });

      if (result.success) {
        console.log(`✅ Login ${i + 1} successful from ${deviceName}`);
      } else if (result.status === 409) {
        console.log(`⚠️  Login ${i + 1} blocked - session limit exceeded from ${deviceName}`);
      } else {
        console.log(`❌ Login ${i + 1} failed from ${deviceName}: ${result.data?.error || 'Unknown error'}`);
      }
    }

    return { clients, results };
  },

  /**
   * Test remote logout functionality
   */
  async testRemoteLogout(authenticatedClients) {
    if (authenticatedClients.length < 2) {
      throw new Error('Need at least 2 authenticated clients for remote logout test');
    }

    const [client1, client2] = authenticatedClients;
    
    console.log('Testing remote logout...');
    
    // Get sessions from client1
    const sessionsResult = await client1.getSessions();
    if (!sessionsResult.success) {
      throw new Error('Failed to get sessions');
    }

    const sessions = sessionsResult.data.sessions;
    console.log(`Found ${sessions.length} active sessions`);

    // Find client2's session
    const client2Session = sessions.find(s => s.sessionId === client2.sessionId);
    if (!client2Session) {
      throw new Error('Could not find client2 session');
    }

    // Revoke client2's session from client1
    console.log(`Revoking session ${client2Session.sessionId.substring(0, 8)}... from ${client1.device.name}`);
    const revokeResult = await client1.revokeSession(client2Session.sessionId);

    if (revokeResult.success) {
      console.log('✅ Remote logout successful');
      
      // Verify client2 can no longer access protected endpoints
      const verifyResult = await client2.getSessions();
      if (verifyResult.status === 401) {
        console.log('✅ Revoked session is properly invalidated');
        return true;
      } else {
        console.log('⚠️  Revoked session still appears to be valid');
        return false;
      }
    } else {
      console.log('❌ Remote logout failed');
      return false;
    }
  }
};

/**
 * Utility functions
 */
const Utils = {
  /**
   * Generate random test user
   */
  generateTestUser() {
    const timestamp = Date.now();
    return {
      name: `Test User ${timestamp}`,
      email: `testuser${timestamp}@example.com`,
      password: 'testpassword123'
    };
  },

  /**
   * Wait for specified milliseconds
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Format session info for display
   */
  formatSessionInfo(session) {
    return {
      id: session.sessionId.substring(0, 8) + '...',
      device: `${session.deviceInfo.browser} on ${session.deviceInfo.os}`,
      location: session.location.ip,
      created: new Date(session.createdAt).toLocaleString(),
      lastActive: new Date(session.lastActiveAt).toLocaleString(),
      isCurrent: session.isCurrent
    };
  }
};

module.exports = {
  APIClient,
  DEVICE_PROFILES,
  TestScenarios,
  Utils
};
