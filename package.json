{"name": "personal-coach", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc --build", "start": "ts-node src/index.ts", "dev": "nodemon --watch src --ext ts --ignore dist --exec ts-node src/index.ts", "test:sessions": "node test-session-management.js", "test:install-deps": "npm install axios colors"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "@types/nodemailer": "^6.4.17", "@types/ua-parser-js": "^0.7.39", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}, "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.0.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "nodemailer": "^7.0.5", "ua-parser-js": "^2.0.4", "zod": "^4.0.5"}}