import { z } from "zod";

// Device Info Schema
export const deviceInfoSchema = z.object({
  userAgent: z.string().min(1, "User agent is required"),
  browser: z.string().min(1, "Browser information is required"),
  os: z.string().min(1, "Operating system information is required"),
  device: z.string().min(1, "Device information is required"),
  isMobile: z.boolean().default(false),
});

// Session Location Schema
export const sessionLocationSchema = z.object({
  ip: z.string(),
  country: z.string().optional(),
  city: z.string().optional(),
  timezone: z.string().optional(),
});

// Enhanced Login Schema with Device Info
export const enhancedLoginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
  deviceInfo: deviceInfoSchema.optional(), 
});

// Session Management Schemas
export const revokeSessionSchema = z.object({
  sessionId: z.string().min(1, "Session ID is required"),
});

export const revokeMultipleSessionsSchema = z.object({
  sessionIds: z.array(z.string().min(1, "Session ID is required"))
    .min(1, "At least one session ID is required")
    .max(10, "Cannot revoke more than 10 sessions at once"),
});

export const getSessionsQuerySchema = z.object({
  page: z.string().regex(/^\d+$/, "Page must be a number").transform(Number).optional(),
  limit: z.string().regex(/^\d+$/, "Limit must be a number").transform(Number).optional(),
});

// Force Logout Schema (when user exceeds session limit)
export const forceLogoutSchema = z.object({
  sessionId: z.string().min(1, "Session ID to revoke is required"),
});

// Session Activity Update Schema
export const updateSessionActivitySchema = z.object({
  sessionId: z.string().min(1, "Session ID is required"),
});

// Validation for session limit configuration
export const sessionLimitConfigSchema = z.object({
  maxConcurrentSessions: z.number().min(1).max(10).default(3),
  sessionExpirationTime: z.string().default("7d"),
  cleanupInterval: z.string().default("1h"),
});

// Schema for device fingerprinting data
export const deviceFingerprintSchema = z.object({
  userAgent: z.string(),
  language: z.string().optional(),
  platform: z.string().optional(),
  cookieEnabled: z.boolean().optional(),
  doNotTrack: z.string().optional(),
  timezone: z.string().optional(),
  screen: z.object({
    width: z.number().optional(),
    height: z.number().optional(),
    colorDepth: z.number().optional(),
  }).optional(),
});

// Response schemas for API documentation
export const sessionResponseSchema = z.object({
  sessionId: z.string(),
  deviceInfo: deviceInfoSchema,
  location: sessionLocationSchema,
  createdAt: z.date(),
  lastActiveAt: z.date(),
  expiresAt: z.date(),
});

export const sessionsListResponseSchema = z.object({
  sessions: z.array(sessionResponseSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
  }),
});

export const sessionLimitExceededResponseSchema = z.object({
  error: z.string(),
  code: z.literal("SESSION_LIMIT_EXCEEDED"),
  activeSessions: z.array(sessionResponseSchema),
  maxSessions: z.number(),
  message: z.string(),
});
