import { z } from "zod";

// Device Info Schema
export const deviceInfoSchema = z.object({
  userAgent: z.string().min(1, "User agent is required"),
  browser: z.string().min(1, "Browser information is required"),
  os: z.string().min(1, "Operating system information is required"),
  device: z.string().min(1, "Device information is required"),
  isMobile: z.boolean().default(false),
});

// Session Location Schema
export const sessionLocationSchema = z.object({
  ip: z.string(),
  country: z.string().optional(),
  city: z.string().optional(),
  timezone: z.string().optional(),
});

// Enhanced Login Schema with Device Info
export const enhancedLoginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
  deviceInfo: deviceInfoSchema.optional(), 
});

// Session Management Schemas
export const revokeSessionSchema = z.object({
  sessionId: z.string().min(1, "Session ID is required"),
});

export const revokeMultipleSessionsSchema = z.object({
  sessionIds: z.array(z.string().min(1, "Session ID is required"))
    .min(1, "At least one session ID is required")
    .max(10, "Cannot revoke more than 10 sessions at once"),
});

export const getSessionsQuerySchema = z.object({
  page: z.string().regex(/^\d+$/, "Page must be a number").transform(Number).optional(),
  limit: z.string().regex(/^\d+$/, "Limit must be a number").transform(Number).optional(),
});

// Force Logout Schema (when user exceeds session limit)
export const forceLogoutSchema = z.object({
  sessionId: z.string().min(1, "Session ID to revoke is required"),
});





// Response schemas for API documentation
export const sessionResponseSchema = z.object({
  sessionId: z.string(),
  deviceInfo: deviceInfoSchema,
  location: sessionLocationSchema,
  createdAt: z.date(),
  lastActiveAt: z.date(),
  expiresAt: z.date(),
});

export const sessionsListResponseSchema = z.object({
  sessions: z.array(sessionResponseSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
  }),
});


