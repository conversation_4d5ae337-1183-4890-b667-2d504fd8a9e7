import { createTransport } from "nodemailer";
import { emailTemplate } from "../constant/emailTemplete";

const sendOurEmail = async (
  email: string,
  title: string,
  isVerificationEmail: boolean = false,
  verificationToken?: string,
  customHtml?: string
) => {
    const transporter = createTransport({
        service: "gmail",
        auth: {
            user: process.env.MAIL_USER,
            pass: process.env.MAIL_PASS,
        },
    });

    let htmlContent = '';

    if (isVerificationEmail) {
        // Use the email template for verification emails
        htmlContent = emailTemplate(email, verificationToken);
    } else if (customHtml) {
        htmlContent = customHtml;
    } else {
        // Simple HTML for other types of emails
        htmlContent = `
            <div style="font-family: Arial, sans-serif; padding: 20px;">
                <h2>Personal Coach</h2>
                <p>Hello,</p>
                <p>Thank you for using Personal Coach!</p>
                <p>Best regards,<br>The Personal Coach Team</p>
            </div>
        `;
    }

    const info = await transporter.sendMail({
        from: '"Personal Coach " <<EMAIL>>',
        to: email,
        subject: title,
        html: htmlContent
    });

    return info;
};

export default sendOurEmail;